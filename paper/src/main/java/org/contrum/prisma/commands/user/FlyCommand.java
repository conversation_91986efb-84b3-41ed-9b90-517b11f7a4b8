package org.contrum.prisma.commands.user;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.tritosa.Translator;

@CommandAlias("fly|volar|flight") @CommandPermission("core.command.fly")
public class FlyCommand extends BaseCommand {

    @Dependency private Translator translator;

    @Default
    @Syntax("")
    public void flySelf(Player player) {
        if (PlayerUtils.toggleFly(player)) {
            translator.send(player, "COMMANDS.FLY.ENABLED");
        } else {
            translator.send(player, "COMMANDS.FLY.DISABLED");
        }
    }

    @Default @CommandPermission("core.command.fly.other")
    @Syntax("<player>")
    public void flyOther(CommandSender sender, OnlinePlayer target) {
        Player player = target.getPlayer();
        if (PlayerUtils.toggleFly(player)) {
            translator.send(sender, "COMMANDS.FLY.ENABLED_OTHER", player);
            translator.send(player, "COMMANDS.FLY.ENABLED");
        } else {
            translator.send(sender, "COMMANDS.FLY.DISABLED_OTHER", player);
            translator.send(player, "COMMANDS.FLY.DISABLED");
        }
    }
}
