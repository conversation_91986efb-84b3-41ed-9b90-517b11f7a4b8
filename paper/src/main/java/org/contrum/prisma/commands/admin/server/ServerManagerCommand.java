package org.contrum.prisma.commands.admin.server;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.redis.RedisBackend;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.prisma.server.ServersService;
import org.contrum.prisma.servermanager.menu.ServerEditorMenu;
import org.contrum.prisma.servermanager.packets.SMRunCmdPacket;
import org.contrum.tritosa.Translator;

@CommandAlias("servermanager|svm")
@CommandPermission("core.command.servermanager")
public class ServerManagerCommand extends BaseCommand {

    @Dependency private PaperServices services;
    @Dependency private ServersService serversService;
    @Dependency private RedisBackend redisBackend;
    @Dependency private Translator translator;

    @Default
    @Syntax("")
    public void help() {

    }

    @Subcommand("menu")
    @Syntax("")
    public void menu(Player sender) {
        new ServerEditorMenu(services).open(sender);
    }

    @Subcommand("runcmd")
    @Syntax("<server> <command>")
    public void runCmd(CommandSender sender, String server, String cmd) {
        sender.sendMessage(CC.translate("&aCommand request sent!"));
        redisBackend.sendPacket(new SMRunCmdPacket(server, cmd));
    }

    @Subcommand("runcmd all")
    public void runCmd(CommandSender sender, String cmd) {
        for (PrismaServer server : serversService.getServers()) {
            if (server.getType() == PrismaServer.ServerType.PROXY || server.getType() == PrismaServer.ServerType.UNKNOWN) continue;

            redisBackend.sendPacket(new SMRunCmdPacket(server.getName(), cmd));
        }
    }
}
