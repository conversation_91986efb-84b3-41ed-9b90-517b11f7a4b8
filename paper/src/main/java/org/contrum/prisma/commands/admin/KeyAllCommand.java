package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.keyall.KeyAllInstance;
import org.contrum.prisma.keyall.KeyAllService;

@CommandAlias("keyall") @CommandPermission("core.command.keyall")
public class KeyAllCommand extends BaseCommand {

    @Dependency private PaperServices paperServices;
    @Dependency private KeyAllService service;

    @Default
    @Syntax("")
    public void test(Player player) {

        KeyAllInstance keyAllInstance = new KeyAllInstance(paperServices, null, null);
        keyAllInstance.start();

    }

}
