package org.contrum.prisma.commands.user;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.ProfileGlobalPaperMetadata;
import org.contrum.prisma.settings.menu.SettingsMenu;

@CommandAlias("settings|ajustes")
public class SettingsCommand extends BaseCommand {

    @Dependency private PaperServices paperServices;

    @Default
    @Syntax("")
    public void command(Player player) {
        Profile profile = paperServices.getProfileService().getProfile(player.getUniqueId());
        ProfileGlobalPaperMetadata metadata = profile.getGlobalMetadata(ProfileGlobalPaperMetadata.class);
        new SettingsMenu(profile, metadata, paperServices, paperServices.getSettingsService()).open(player);
    }
}
