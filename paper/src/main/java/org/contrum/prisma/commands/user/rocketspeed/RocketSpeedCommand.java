package org.contrum.prisma.commands.user.rocketspeed;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.commands.user.rocketspeed.menu.RocketSpeedMenu;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;

@CommandAlias("cohetes|rocketspeed") @CommandPermission("core.command.rocketspeed")
public class RocketSpeedCommand extends BaseCommand {

    @Dependency private PaperServices services;

    @Default
    @Syntax("")
    public void onCommand(Player player) {
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        new RocketSpeedMenu(services, metadata).open(player);
    }

}
