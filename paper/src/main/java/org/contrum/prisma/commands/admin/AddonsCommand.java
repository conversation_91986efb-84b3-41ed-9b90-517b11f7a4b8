package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Dependency;
import co.aikar.commands.annotation.Subcommand;
import org.bukkit.command.CommandSender;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.addon.Addon;

@CommandAlias("addon") @CommandPermission("core.command.addon")
public class AddonsCommand extends BaseCommand {

    @Dependency private PaperServices services;

    @Subcommand("list")
    @Syntax("")
    public void list(CommandSender sender) {
        sender.sendMessage(CC.translate("&eAddons:"));
        for (Addon addon : services.getAddonService().getAddons().values()) {
            sender.sendMessage(CC.translate("&e- " + addon.getAddonName() + " - enabled: &c" + addon.isLoaded()));
        }
    }

    @Subcommand("reload")
    @Syntax("<addon>")
    public void reload(CommandSender sender, String addonName) {
        sender.sendMessage(CC.translate("&eAddons:"));
        Addon addon = services.getAddonService().getAddon(addonName);

        if (addon == null) {
            sender.sendMessage(CC.translate("&cAddon doesn't exist!"));
            return;
        }

        addon.onReload();
        sender.sendMessage(CC.translate("&aAddon has been reloaded!"));
    }
}
