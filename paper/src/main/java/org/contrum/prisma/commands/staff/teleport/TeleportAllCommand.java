package org.contrum.prisma.commands.staff.teleport;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.metadata.FixedMetadataValue;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

@CommandAlias("teleportall|tpall")
@CommandPermission("core.command.teleportall")
public class TeleportAllCommand extends BaseCommand {

    @Dependency private Translator translator;
    @Dependency private PaperServices services;

    @Default
    @Syntax("")
    public void teleportAll(Player sender) {
        translator.send(sender, "COMMANDS.TELEPORTALL.CONFIRMATION", LocalPlaceholders.builder().add("<count>", String.valueOf(Bukkit.getOnlinePlayers().size() - 1)));
        sender.setMetadata("teleportall", new FixedMetadataValue(services.getPlugin(), true));
    }

    @Subcommand("confirm")
    @Syntax("")
    public void teleportAllConfirm(Player sender) {
        if (!sender.hasMetadata("teleportall")) {
            translator.send(sender, "COMMANDS.TELEPORTALL.NOT_REQUESTED");
            return;
        }

        Bukkit.getOnlinePlayers().forEach(player -> {
            if (player != sender) {
                player.teleport(sender);
                translator.send(player, "COMMANDS.TELEPORTALL.TELEPORTED_TO", sender);
            }
        });

        translator.send(sender, "COMMANDS.TELEPORTALL.TELEPORTED",
                LocalPlaceholders.builder().add("<count>", String.valueOf(Bukkit.getOnlinePlayers().size() - 1)));
    }

    @Subcommand("cancel")
    @Syntax("")
    public void teleportAllCancel(Player sender) {
        if (!sender.hasMetadata("teleportall")) {
            translator.send(sender, "COMMANDS.TELEPORTALL.NOT_REQUESTED");
            return;
        }

        sender.removeMetadata("teleportall", services.getPlugin());
        translator.send(sender, "COMMANDS.TELEPORTALL.CANCELLED");
    }
}
