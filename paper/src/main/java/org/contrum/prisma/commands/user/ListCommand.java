package org.contrum.prisma.commands.user;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.rank.RankService;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@CommandAlias("list")
public class ListCommand extends BaseCommand {

    @Dependency private ProfileService profileService;
    @Dependency private RankService rankService;
    @Dependency private Translator translator;

    @Default
    @Syntax("")
    public void execute(CommandSender sender) {
        List<Profile> onlineProfile = profileService.getOnlineProfiles().values().stream().sorted(
            (profile1, profile2) -> profile2.getActiveGrant().getRank().getWeight() - profile1.getActiveGrant().getRank().getWeight())
                .toList();

        List<String> names = new ArrayList<>();

        for (Profile profile : onlineProfile) {
            names.add(profile.getColoredName());
        }

        String rankLists = String.join("&7, &a", rankService.getRanks().stream()
                        .map(map -> map.getColor() + map.getName())
                        .collect(Collectors.joining("&7, ")));

        LocalPlaceholders placeholders = LocalPlaceholders.builder()
                .add("<online>", String.valueOf(Bukkit.getOnlinePlayers().size()))
                .add("<max_players>", String.valueOf(Bukkit.getMaxPlayers()))
                .add("<ranks>", rankLists)
                .add("<names>", String.join(",", names));

        translator.send(sender, "COMMANDS.LIST_FORMAT", placeholders);
    }

}
