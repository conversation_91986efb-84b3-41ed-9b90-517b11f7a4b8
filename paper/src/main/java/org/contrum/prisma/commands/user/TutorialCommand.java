package org.contrum.prisma.commands.user;

import co.aikar.commands.annotation.*;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.commands.ToggleableBaseCommand;

@CommandAlias("tutorial") @CommandPermission("core.command.devvvv")
public class TutorialCommand extends ToggleableBaseCommand {

    @Dependency private PaperServices services;

    @Default
    @Syntax("")
    public void start(Player player) {
        services.getTutorialService().startTutorial(player);
    }

    @Subcommand("cancel")
    @Syntax("")
    public void cancel(Player player) {
        if (services.getTutorialService().cancelTutorial(player)) {
            
        }
    }

    @Override
    public boolean isEnabled() {
        return services.getTutorialService().isEnabled();
    }
}
