/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 26/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.treasurechest.TreasureChestService;

@CommandAlias("treasurechest")
@CommandPermission("apple.treasurechest.manage")
public class TreasureChestCommand extends BaseCommand {

    @Dependency
    private TreasureChestService manager;

    @Subcommand("create")
    @CommandCompletion("@treasureChestTypes")
    @Syntax("<type>")
    public void create(Player player, String type){
        ItemStack chestItem = manager.getChestItem(type);
        if (chestItem == null){
            player.sendMessage(CC.translate("&cChest type doesn't exist!"));
            return;
        }
        player.getInventory().addItem(chestItem);
    }

    @Subcommand("refill")
    @Syntax("")
    public void refill(CommandSender player){
        manager.refillChests();
        player.sendMessage(CC.translate("&aAll chests have been refilled!"));
    }

    @Subcommand("save")
    public void save(CommandSender player){
        manager.save();
        player.sendMessage(CC.translate("&aConfig saved!"));
    }
}
