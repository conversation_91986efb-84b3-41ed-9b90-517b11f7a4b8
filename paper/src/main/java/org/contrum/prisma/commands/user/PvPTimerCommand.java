package org.contrum.prisma.commands.user;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.systems.impl.newbieprotection.NewbieProtectionSystem;
import org.contrum.tritosa.Translator;

@CommandAlias("pvp|pvptimer")
public class PvPTimerCommand extends BaseCommand {
    @Dependency private ProfileService profileService;
    @Dependency private Translator translator;
    @Dependency private NewbieProtectionSystem system;

    @Default
    @Syntax("")
    public void status(Player player) {
        ProfilePaperMetadata metadata = profileService.getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);

        if (system.hasProtection(metadata)) {
            translator.send(player, "COMMANDS.NEWBIE_PROTECTION.PROTECTED", system.getRemaining(metadata));
        } else {
            translator.send(player, "COMMANDS.NEWBIE_PROTECTION.NOT_PROTECTED");
        }
    }

    @Subcommand("enable")
    public void enable(Player player) {
        Profile profile = profileService.getProfile(player.getUniqueId());

        if (!system.disableProtection(profile)) {
            translator.send(player, "COMMANDS.NEWBIE_PROTECTION.NOT_PROTECTED");
            return;
        }

        translator.send(player, "COMMANDS.NEWBIE_PROTECTION.DISABLED");
    }

    @Subcommand("disableProtection") @CommandPermission("core.command.pvptimer.manage")
    public void disableTo(CommandSender sender, OnlinePlayer target) {
        Player player = target.getPlayer();
        Profile profile = profileService.getProfile(player.getUniqueId());

        if (!system.disableProtection(profile)) {
            translator.send(sender, "COMMANDS.NEWBIE_PROTECTION.TARGET_NOT_PROTECTED", player);
            return;
        }

        translator.send(sender, "COMMANDS.NEWBIE_PROTECTION.DISABLED_TO", player);
    }

    @Subcommand("enableProtection") @CommandPermission("core.command.pvptimer.manage")
    public void enableTo(CommandSender sender, OnlinePlayer target) {
        Player player = target.getPlayer();

        if (system.enableProtection(player)) {
            translator.send(sender, "COMMANDS.NEWBIE_PROTECTION.TARGET_ALREADY_PROTECTED", player);
            return;
        }

        translator.send(sender, "COMMANDS.NEWBIE_PROTECTION.ENABLED_TO", player);
    }
}
