package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.entity.Player;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.gameevents.Events;

@CommandAlias("eventmanage")
@CommandPermission("core.command.eventmanage")
public class EventsCommand extends BaseCommand {

    @Dependency private PaperServices services;

    @Subcommand("start")
    @CommandCompletion("@gameevents")
    @Syntax("[event]")
    public void start(Player player, @Optional String event){
        if (event != null && !event.isEmpty()){
            try {
                Events events = Events.valueOf(event);
                services.getGameEventsService().setActiveEvent(events, false);
                return;
            } catch (IllegalArgumentException ignore){
                player.sendMessage(CC.translate("&cEl evento &e" + event + " &cno existe!"));
                return;
            }
        }
        player.sendMessage(CC.translate("&aIniciando un nuevo evento..."))
        ;
        services.getGameEventsService().startRandomEvent(false);
    }

    @Subcommand("forcestart")
    @CommandCompletion("@gameevents")
    @Syntax("[event]")
    public void forcestart(Player player, @Optional String event){
        if (event != null && !event.isEmpty()){
            try {
                Events events = Events.valueOf(event);
                services.getGameEventsService().setActiveEvent(events, false);
                return;
            } catch (IllegalArgumentException ignore){
                player.sendMessage(CC.translate("&cEl evento &e" + event + " &cno existe!"));
                return;
            }
        }
        player.sendMessage(CC.translate("&aIniciando un nuevo evento..."));
        services.getGameEventsService().startRandomEvent(true);
    }
}
