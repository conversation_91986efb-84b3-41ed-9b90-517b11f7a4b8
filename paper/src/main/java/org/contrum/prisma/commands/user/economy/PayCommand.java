package org.contrum.prisma.commands.user.economy;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.economy.EconomyService;
import org.contrum.prisma.events.CurrencyAddEvent;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.profile.currency.Currency;
import org.contrum.prisma.profile.log.balance.PayLog;
import org.contrum.prisma.profile.log.balance.menu.PayHistoryMenu;
import org.contrum.prisma.profile.metadata.ProfileGlobalPaperMetadata;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.settings.impl.PayMode;
import org.contrum.prisma.utils.CCP;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.UUID;

@CommandAlias("pay|pagar|transferir")
public class PayCommand extends BaseCommand {

    @Dependency private PaperServices services;
    @Dependency private Translator translator;
    @Dependency private ProfileService profileService;

    @Default
    @Syntax("<jugador> <cantidad>")
    @CommandCompletion("@players @range:1-1000")
    public void command(Player sender, String name, long amount){
        Player target = Bukkit.getPlayerExact(name);
        if (target == null){
            translator.send(sender, "COMMANDS.PAY.TARGET_NOT_FOUND");
            return;
        }

        if (amount < 1) {
            translator.send(sender, "COMMANDS.PAY.INVALID_AMOUNT");
            return;
        }

        Profile senderProfile = profileService.getProfile(sender.getUniqueId());
        Profile targetProfile = profileService.getProfile(target.getUniqueId());
        ProfilePaperMetadata senderMetadata = senderProfile.getServerMetadata(profileService, ProfilePaperMetadata.class);
        ProfilePaperMetadata targetMetadata = targetProfile.getServerMetadata(profileService, ProfilePaperMetadata.class);

        ProfileGlobalPaperMetadata targetGlobalMetadata = targetMetadata.getProfile().getGlobalMetadata(ProfileGlobalPaperMetadata.class);
        if (targetGlobalMetadata.getSettings().getSetting(PayMode.class).getValue() == PayMode.Value.OFF) {
            translator.send(sender, "COMMANDS.PAY.TARGET_DOES_NOT_ACCEPT_PAYMENTS", target);
            return;
        }

        if (senderMetadata.getCurrency(Currency.COINS) < amount){
            translator.send(sender, "COMMANDS.PAY.NOT_ENOUGH_MONEY", target);
            return;
        }

        senderMetadata.addPayLog(new PayLog(PayLog.Type.REMOVE, targetProfile.getRealName(), targetMetadata.getCurrency(Currency.COINS), amount, senderMetadata.getBalance(Currency.COINS)));
        targetMetadata.addPayLog(new PayLog(PayLog.Type.ADD, senderProfile.getRealName(), senderMetadata.getCurrency(Currency.COINS), amount, targetMetadata.getBalance(Currency.COINS)));

        senderMetadata.withdrawCurrency(Currency.COINS, amount);
        targetMetadata.depositCurrency(services.getPlugin(), target, Currency.COINS, CurrencyAddEvent.Cause.COMMAND, amount);
        translator.send(sender, "COMMANDS.PAY.COINS_SENT", target, LocalPlaceholders.builder().add("<coins_amount>", EconomyService.formatCoins(senderMetadata, amount)));
        translator.send(target, "COMMANDS.PAY.COINS_RECEIVED", sender, LocalPlaceholders.builder().add("<coins_amount>", EconomyService.formatCoins(targetMetadata, amount)));
    }

    @Subcommand("history")
    @Syntax("")
    public void historySelf(Player sender) {
        Profile profile = profileService.getProfile(sender.getUniqueId());
        new PayHistoryMenu(services, profile).open(sender);
    }

    @Subcommand("history")
    @Syntax("<player>") @CommandCompletion("@players") @CommandPermission("core.command.pay.history.other")
    public void historyOther(Player sender, UUID target) {
        profileService.getOrLoadProfileAsync(target).whenComplete((profile, ex) -> {
            if (profile == null) {
                translator.send(sender, "ERRORS.COULDNT_FIND_USER");
                return;
            }

            TaskUtil.run(services.getPlugin(), () -> new PayHistoryMenu(services, profile).open(sender));
        });
    }
}