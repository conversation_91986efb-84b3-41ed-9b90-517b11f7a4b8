package org.contrum.prisma.commands.user;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.systems.impl.battlepass.metadata.ProfileBattlePassMetadata;
import org.contrum.prisma.timedrewards.menu.TimedRewardsMenu;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.UUID;

@CommandAlias("timedrewards|rewards|recompensas")
public class TimedRewardsCommand extends BaseCommand {

    @Dependency
    private PaperServices services;
    @Dependency
    private Translator translator;

    @Default
    @Syntax("")
    public void onCommand(Player player) {
        new TimedRewardsMenu(services).open(player);
    }

    @Subcommand("reset") @CommandPermission("core.command.timedrewards.admin")
    @Syntax("<player>")
    public void reset(Player player, UUID target) {
        services.getProfileService().getOrLoadProfileAsync(target).whenComplete(((profile, throwable) -> {
            if (profile == null) {
                translator.send(player, "ERRORS.COULDNT_FIND_USER");
                return;
            }

            ProfilePaperMetadata metadata = profile.getServerMetadata(services, ProfilePaperMetadata.class);
            metadata.getClaimedTimedRewards().clear();

            services.getProfileService().saveIfOffline(profile);
        }));
    }
}
