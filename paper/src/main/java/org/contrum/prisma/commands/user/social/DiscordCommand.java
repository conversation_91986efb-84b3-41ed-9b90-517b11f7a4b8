/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by EnzoLy on 16/04/2024
 * Website: contrum.org
 */

package org.contrum.prisma.commands.user.social;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.command.CommandSender;
import org.contrum.tritosa.Translator;

@CommandAlias("discord|dc")
public class DiscordCommand extends BaseCommand {

    @Dependency
    private Translator translator;

    @Default
    @Syntax("")
    public void twitter(CommandSender sender) {
        translator.send(sender, "COMMANDS.SOCIAL.DISCORD");
    }

}

