package org.contrum.prisma.commands.user.mines;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.commands.user.mines.menu.MinesMenu;
import org.contrum.prisma.utils.commands.ToggleableBaseCommand;

import java.util.HashMap;
import java.util.UUID;

;

@CommandAlias("mines|minas")
public class MinesCommand extends ToggleableBaseCommand {

    @Dependency private PaperServices services;

    public static HashMap<UUID, Long> cooldown = new HashMap<>();
    @Default
    @Syntax("")
    public void command(Player player){
        new MinesMenu(services, player).open(player);
    }

    @Override
    public boolean isEnabled() {
        return PrismaCoreSetting.MINES_COMMAND_ENABLED;
    }
}
