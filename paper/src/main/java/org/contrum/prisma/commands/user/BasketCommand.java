package org.contrum.prisma.commands.user;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.bukkit.command.CommandSender;
import org.bukkit.command.ConsoleCommandSender;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

@CommandAlias("basket")
@CommandPermission("core.basket.use")
public class BasketCommand extends BaseCommand {

    @Dependency private PaperServices services;
    @Dependency private Translator translator;

    @Default
    @Syntax("<target> <context...>")
    @CommandCompletion("@players")
    public void basket(CommandSender sender, OnlinePlayer targetOnline, String[] context) {
        // Validate arguments
        if (context.length == 0) {
            translator.send(sender, "COMMANDS.USAGE", LocalPlaceholders.builder().add("<usage>", "/basket <target> <context>"));
            return;
        }

        // Limit context length to prevent spam
        String message = String.join(" ", context);
        if (message.length() > 256) {
            translator.send(sender, "ERRORS.MESSAGE_TOO_LONG");
            return;
        }

        // Get BasketName list from PrismaCoreSettings
        List<String> basketNames = PrismaCoreSetting.BASKET_NAMES;

        // Validate BasketName configuration
        if (basketNames == null || basketNames.isEmpty()) {
            translator.send(sender, "ERRORS.BASKET_EMPTY_CONFIG");
            return;
        }

        // Get target player
        Player target = targetOnline.getPlayer();

        // Select random name from BasketName list
        String randomName = basketNames.get(ThreadLocalRandom.current().nextInt(basketNames.size()));

        // Create placeholders for the message
        LocalPlaceholders placeholders = LocalPlaceholders.builder()
                .add("<basket_name>", randomName)
                .add("<message>", message);

        // Send the formatted message to the target player using translator
        translator.send(target, "BASKET.MESSAGE_FORMAT", placeholders);

        // Send confirmation to sender (only if not console)
        if (!(sender instanceof ConsoleCommandSender)) {
            LocalPlaceholders confirmPlaceholders = LocalPlaceholders.builder()
                    .add("<target_name>", target.getName())
                    .add("<basket_name>", randomName);
            translator.send(sender, "BASKET.SENT_CONFIRMATION", confirmPlaceholders);
        }
    }
}
