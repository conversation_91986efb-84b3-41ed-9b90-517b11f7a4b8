package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Dependency;
import co.aikar.commands.annotation.Subcommand;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.systems.impl.dtc.DTCSystem;

@CommandAlias("dtc") @CommandPermission("core.command.dtc")
public class DTCCommand extends BaseCommand {

    @Dependency private DTCSystem system;

    @Subcommand("forcespawn")
    @Syntax("")
    public void spawn(CommandSender sender) {
        system.startDTC();
    }

    @Subcommand("stop")
    @Syntax("")
    public void stop(CommandSender sender) {
        system.stopDTC();
    }

    @Subcommand("setLocation")
    @Syntax("")
    public void setLocation(Player player) {
        system.setLocation(player.getLocation());
        player.sendMessage(CC.translate("&aLocation set!"));
    }
}
