package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import lombok.SneakyThrows;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ColorableArmorMeta;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.clients.PlayerClient;
import org.contrum.prisma.statistics.StatisticsService;
import org.contrum.prisma.utils.time.TimeUtils;

import java.awt.*;
import java.time.Duration;
import java.util.Set;
import java.util.UUID;

@CommandAlias("dev")
@CommandPermission("core.command.dev")
public class DevCommand extends BaseCommand {

    @Dependency private PaperServices services;

    @Subcommand("playerdebug|pdebug")
    @Syntax("<target>")
    public void playerDebug(Player sender, UUID target) {
        services.getProfileService().getOrLoadProfileAsync(target).whenComplete((profile, throwable) -> {
            if (throwable != null) {
                throwable.printStackTrace();
                return;
            }

            if (profile == null) {
                sender.sendMessage("§cCould not find profile.");
                return;
            }

            sender.sendMessage("§eProfile Information for " + profile.getName());
            sender.sendMessage("§eUUID: §f" + profile.getUniqueId());
            sender.sendMessage("§eDisplay Name: §f" + profile.getDisplayName());
            sender.sendMessage("§eColored Name: §f" + profile.getColoredName());
            sender.sendMessage("§eLast Seen: §f" + profile.getGlobalLastSeen());
            sender.sendMessage("§eLast Server: §f" + profile.getLastServer().getName());
            sender.sendMessage("§eLast Address: §f" + profile.getLastAddress());
            sender.sendMessage("§eRank: §f" + profile.getActiveGrant().getRank().getName());
        });
    }

    @SneakyThrows
    @Subcommand("waypoint")
    @Syntax("<target> <waypoint_name> <color>")
    public void spawnWaypoint(Player sender, OnlinePlayer target, String name, String color, Duration duration) {
        PlayerClient client = services.getClientsService().getClient(target.getPlayer());

        Color c = (Color) Color.class.getDeclaredField(color.toUpperCase()).get(null);
        if (c == null) {
            sender.sendMessage(CC.translate("&cColor doesn't exist!"));
            return;
        }

        client.createWaypoint(target.getPlayer(), name, sender.getLocation(), c, duration);
    }

    @Subcommand("hasPermission")
    @Syntax("<player> <permission>")
    public void check(CommandSender sender, OnlinePlayer player, String permission) {
        Player target = player.getPlayer();
        sender.sendMessage(target.getName() + " has permission: " + target.hasPermission(permission));
    }

    @Subcommand("channels")
    @Syntax("")
    public void channels(CommandSender sender) {
        Set<String> incomingChannels = Bukkit.getServer().getMessenger().getIncomingChannels();

        for (String s : incomingChannels) {
            sender.sendMessage(s);
        }
    }

    @Subcommand("time")
    @Syntax("<duration> <detailed>")
    public void time(CommandSender sender, Duration duration, boolean detailed) {
        sender.sendMessage(TimeUtils.getFormattedTime(duration.toMillis(), detailed));
    }

    @Subcommand("statistics send")
    public void send(CommandSender sender) {
        services.getStatisticsService().save(services.getStatisticsService().getCurrentHourStatistic());
    }

    @Subcommand("statistics log")
    public void log(CommandSender sender) {
        StatisticsService service = services.getStatisticsService();
        sender.sendMessage("HourId: " + service.getCurrentHourStatistic().getId());
        sender.sendMessage("Data: " + " (" + service.getCurrentHourStatistic().formatId() + ")");
    }

    @Subcommand("statistics setHourId")
    public void changeId(CommandSender sender, int id) {
        services.getStatisticsService().getCurrentHourStatistic().setId(id);
    }

    @Subcommand("bedrockMenu") @CommandAlias("bedrock")
    public void changeId(CommandSender sender, OnlinePlayer target) {
    }

    @Subcommand("dye")
    public void dye(Player player) {
        ItemStack item = player.getInventory().getItemInMainHand();
        if (!(item.hasItemMeta() || item.getItemMeta() instanceof ColorableArmorMeta)) {
            item.setItemMeta(Bukkit.getItemFactory().getItemMeta(Material.LEATHER_BOOTS));
        }

        ColorableArmorMeta itemMeta = ((ColorableArmorMeta) item.getItemMeta());
        itemMeta.setColor(org.bukkit.Color.RED);
    }
}