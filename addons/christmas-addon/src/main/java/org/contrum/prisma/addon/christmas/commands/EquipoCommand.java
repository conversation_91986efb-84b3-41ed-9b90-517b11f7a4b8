package org.contrum.prisma.addon.christmas.commands;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.entity.Player;
import org.contrum.prisma.addon.christmas.event.SpecialEventServiceSystem;
import org.contrum.prisma.addon.christmas.event.christmas.ChristmasEvent;

@CommandAlias("equipo")
public class EquipoCommand extends BaseCommand {

    @Dependency private SpecialEventServiceSystem service;

    @Default
    @Syntax("")
    public void equipo(Player player) {
        this.getEvent().join(player);
    }

    private ChristmasEvent getEvent() {
        return (ChristmasEvent) service.getEvent("Christmas");
    }
}
