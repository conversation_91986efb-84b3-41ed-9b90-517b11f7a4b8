package org.contrum.prisma.addon.lifesteal.commands;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.bukkit.entity.Player;
import org.contrum.prisma.addon.lifesteal.Addon;
import org.contrum.prisma.addon.lifesteal.systems.TeleportRequestSystem;

@CommandAlias("tpa")
public class TeleportRequestCommand extends BaseCommand {
    @Dependency private Addon addon;
    @Dependency private TeleportRequestSystem teleportSystem;

    @Default
    @CommandCompletion("@players")
    @Syntax("<player>")
    public void tpa(Player player, OnlinePlayer target) {
        teleportSystem.teleportRequest(player, target.getPlayer());
    }

    @CommandAlias("tpaccept") @Subcommand("accept")
    @Syntax("")
    public void acceptAny(Player player) {
        teleportSystem.teleportAccept(player);
    }

    @CommandAlias("tpaccept") @Subcommand("accept")
    @Syntax("<player>")
    public void accept(Player player, OnlinePlayer target) {
        teleportSystem.teleportAccept(player, target.getPlayer());
    }

    @CommandAlias("tpadeny") @Subcommand("deny")
    @Syntax("")
    public void denyAny(Player player) {
        teleportSystem.teleportDeny(player);
    }

    @CommandAlias("tpadeny") @Subcommand("deny")
    @Syntax("<player>")
    public void deny(Player player, OnlinePlayer target) {
        teleportSystem.teleportDeny(player, target.getPlayer());
    }
}
