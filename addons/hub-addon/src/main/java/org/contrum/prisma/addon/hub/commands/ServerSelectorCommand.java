package org.contrum.prisma.addon.hub.commands;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.addon.hub.menus.GeyserGameSelectorMenu;
import org.contrum.prisma.addon.hub.settings.PrismaHubSettings;
import org.contrum.prisma.profile.metadata.ProfileGlobalPaperMetadata;
import org.contrum.prisma.settings.impl.BedrockFormsMode;
import org.contrum.prisma.utils.GeyserUtil;

@CommandAlias("serverselector|jugar")
public class ServerSelectorCommand extends BaseCommand {

    @Dependency
    private PaperServices services;

    @Default
    @Syntax("")
    public void open(Player player) {

        if (GeyserUtil.isGeyser(player)) {
            ProfileGlobalPaperMetadata metadata = services.getProfileService().getGlobalMetadata(player.getUniqueId(), ProfileGlobalPaperMetadata.class);
            if (metadata.getSettings().getSetting(BedrockFormsMode.class).getValue() == BedrockFormsMode.Value.ENABLED) {
                new GeyserGameSelectorMenu().open(player);
                return;
            }
        }

        player.performCommand(PrismaHubSettings.SERVER_SELECTOR_COMMAND);
    }
}