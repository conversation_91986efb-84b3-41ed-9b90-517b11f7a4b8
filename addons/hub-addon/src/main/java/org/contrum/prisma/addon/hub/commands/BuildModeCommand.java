package org.contrum.prisma.addon.hub.commands;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.metadata.FixedMetadataValue;
import org.contrum.prisma.addon.hub.Addon;
import org.contrum.tritosa.Translator;

@CommandAlias("buildmode") @CommandPermission("core.command.buildmode")
public class BuildModeCommand extends BaseCommand {

    @Dependency private Addon addon;
    @Dependency private Translator translator;

    @Default
    @Syntax("[player]")
    public void toggle(Player sender, @Optional OnlinePlayer target) {
        Player player = target != null ? target.getPlayer() : sender;

        if (player.hasMetadata("buildMode")) {
            player.removeMetadata("buildMode", addon);
            translator.send(sender, target == null ? "COMMANDS.BUILD_MODE.DISABLED" : "COMMANDS.BUILD_MODE.DISABLED_OTHER", player);
        } else {
            player.setMetadata("buildMode", new FixedMetadataValue(addon, true));
            translator.send(sender, target == null ? "COMMANDS.BUILD_MODE.ENABLED" : "COMMANDS.BUILD_MODE.ENABLED_OTHER", player);
        }
    }
}
